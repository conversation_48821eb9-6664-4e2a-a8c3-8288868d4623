# Grade Spark Academy Project Memory

## Project Overview
Grade Spark Academy is an academic assistance platform designed to help university students with assignments and projects. The website aims to provide expert assistance, reduce academic stress, and help students achieve better grades while maintaining ethical standards.

## Technical Stack
- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Routing**: React Router (HashRouter)
- **Animation**: Framer Motion
- **3D Graphics**: Three.js with React Three Fiber
- **Form Handling**: HubSpot Forms integration
- **Analytics**: Google Analytics 4 via Google Tag Manager

## Project Structure
- Single-page application (SPA) with client-side routing
- Lazy-loaded page components for performance optimization
- Component-based architecture with reusable UI elements
- Responsive design for all device sizes

## Key Features
- Academic assistance services for university students
- Expert matching system for assignments and projects
- Transparent process flow from order to delivery
- Subject coverage across multiple academic disciplines
- Testimonials and trust signals to build credibility

## Analytics Implementation
- Google Tag Manager (GTM ID: GTM-PWTGRCTV)
- Google Analytics 4 (GA4 ID: G-D2VFYV0XX0) - This is the tracking ID that should be used
- Microsoft Clarity for session recording and heatmaps
- Custom event tracking for user interactions
- Privacy-focused consent management for GDPR compliance

## Performance Optimization
- Code splitting with React.lazy() and Suspense
- Manual chunk splitting in Vite configuration
- Image optimization with lazy loading
- Component optimization to reduce re-renders

## Content Strategy
- Clear, benefit-oriented messaging focused on academic support
- Emphasis on trust signals (originality, confidentiality, on-time delivery)
- Professional yet empathetic tone to connect with stressed students
- Detailed service descriptions with clear value propositions

## User Flow
1. Landing on homepage with clear value proposition
2. Exploring services and academic coverage
3. Understanding the process through "How It Works" section
4. Building trust through guarantees and testimonials
5. Taking action via contact forms or direct order placement


