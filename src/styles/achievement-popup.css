@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

.achievement-popup-enter {
  opacity: 0;
  transform: scale(0.9);
}

.achievement-popup-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.achievement-popup-exit {
  opacity: 1;
  transform: scale(1);
}

.achievement-popup-exit-active {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

/* Add responsive styles */
@media (max-width: 640px) {
  .achievement-popup {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
}

.achievement-card-section {
  margin-bottom: 2rem; /* 32px */
}

.achievement-card-section:last-child {
  margin-bottom: 0;
}

/* Adjust spacing for mobile */
@media (max-width: 640px) {
  .achievement-card-section {
    margin-bottom: 1.5rem; /* 24px */
  }
  
  .achievement-popup {
    margin: 1rem;
    max-width: calc(100% - 2rem);
  }
} 