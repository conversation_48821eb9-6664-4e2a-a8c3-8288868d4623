/* Animated Background Styles */

:root {
  --cyan-main: #00e1ff;
  --cyan-glow: rgba(0, 225, 255, 0.5);
  --cyan-dark: #00a8bf;
}

.animated-bg-section {
  position: relative;
  overflow: hidden;
}

.gradient-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at center, rgba(0, 225, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
  opacity: 0;
  animation: fadeIn 2s ease 0.5s forwards, pulseBg 6s ease-in-out infinite;
  pointer-events: none;
  z-index: 0;
}

.particles-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  background: var(--cyan-main);
  border-radius: 50%;
  opacity: 0;
  filter: blur(1px);
}

/* Animations */
@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

@keyframes pulseBg {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.1);
  }
}

/* Three.js canvas styling */
.three-bg-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}
