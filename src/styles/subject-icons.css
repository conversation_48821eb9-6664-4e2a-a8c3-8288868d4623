/* --- Base Variables and Styles --- */
:root {
  --night: #151616ff;
  --white: #FEFEFEff;
  --celeste: #A0EBEBff;
  --night-2: #0F0F0Fff;
  --white-2: #FFFFFFff;
    --white: #FFFFFF;

    /* Subject Group Palettes - Adjusted for Professional Look */
    --math-sci-blue: #2563EB;
    /* Stronger Blue */
    --math-sci-blue-light: #BFDBFE;
    /* Lighter Blue */
    --bio-chem-green: #059669;
    /* Emerald Green */
    --bio-chem-green-light: #A7F3D0;
    /* Lighter Green */
    --business-econ-orange: #F59E0B;
    /* Amber */
    --business-econ-orange-light: #FEF3C7;
    /* Lighter Amber */
    --humanities-purple: #7C3AED;
    /* Vibrant Purple */
    --humanities-purple-light: #DDD6FE;
    /* Lighter Purple */
    --accent-pink: #EC4899;
    /* Vibrant Pink */
    --accent-red: #EF4444;
    /* Red for emphasis */

    /* Professional Greys */
    --grey-light: #F3F4F6;
    --grey-medium: #9CA3AF;
    --grey-dark: #4B5563;

    /* Animation Durations */
    --fast-duration: 2.5s;
    --medium-duration: 4s;
    --slow-duration: 6s;
    --very-slow-duration: 10s;
}

/* Remove global body styles that were breaking layout */
/* Subject icons should be styled within their containers only */

/* --- Common Icon Wrapper --- */
.icon-wrapper {
    width: 90px;
    /* Slightly larger */
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    font-family: 'Inter', sans-serif;
    background-color: var(--white);
    /* Use pure white */
    border-radius: 16px;
    /* More rounded */
    overflow: hidden;
    border: 1px solid var(--border-light);
    /* Enhanced shadow for more depth */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    /* Add hover effect */
}

.icon-wrapper:hover {
    transform: translateY(-4px) scale(1.02);
    /* Lift effect on hover */
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08), 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* --- Accounting - Dynamic Calculator --- */
.icon-accounting .calculator-body {
    width: 50px;
    /* Slightly larger */
    height: 60px;
    background: linear-gradient(145deg, #f9fafb, #e5e7eb);
    /* Refined gradient */
    border-radius: 8px;
    position: relative;
    border: 1px solid var(--border-light);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.06), 0 3px 6px rgba(0, 0, 0, 0.06);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 6px;
    /* Adjusted padding */
}

.icon-accounting .screen {
    width: 40px;
    height: 14px;
    background-color: #D1FAE5;
    /* Lighter green screen */
    border-radius: 4px;
    margin-bottom: 6px;
    font-size: 9px;
    /* Slightly larger */
    font-family: 'Courier New', Courier, monospace;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 4px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.icon-accounting .screen::after {
    content: '1,234';
    /* Start simple */
    position: absolute;
    right: 4px;
    /* More dynamic number sequence */
    animation: calcNumbersDynamic var(--slow-duration) infinite steps(8, end);
}

@keyframes calcNumbersDynamic {
    0% {
        content: '1,234';
    }

    12% {
        content: '+ 567';
    }

    24% {
        content: '= 1,801';
    }

    36% {
        content: '/ 7';
    }

    48% {
        content: '= 257.28';
    }

    60% {
        content: '* 10';
    }

    72% {
        content: '= 2,572';
    }

    84% {
        content: 'TOTAL';
    }

    100% {
        content: '2,572';
    }
}

.icon-accounting .buttons {
    display: grid;
    grid-template-columns: repeat(4, 10px);
    /* Larger buttons */
    gap: 3px;
    padding: 0 5px;
}

.icon-accounting .button {
    width: 10px;
    height: 8px;
    background-color: var(--white);
    border: 1px solid var(--border-light);
    border-radius: 3px;
    /* More rounded */
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    /* More impactful button press */
    animation: pressButtonAccountingImpact var(--medium-duration) infinite ease-in-out;
}

/* Staggered animation delays */
.icon-accounting .button:nth-child(4n+1) {
    animation-delay: 0.1s;
}

.icon-accounting .button:nth-child(4n+2) {
    animation-delay: 0.4s;
}

.icon-accounting .button:nth-child(4n+3) {
    animation-delay: 0.7s;
}

.icon-accounting .button:nth-child(4n+4) {
    animation-delay: 1.0s;
}

@keyframes pressButtonAccountingImpact {

    0%,
    100% {
        transform: translateY(0) scale(1);
        background-color: var(--white);
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    }

    5%,
    15% {
        transform: translateY(1px) scale(0.95);
        /* Press down and slightly shrink */
        background-color: var(--accent-pink);
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
        /* Deeper inset shadow */
    }

    10% {
        /* Quick pop back */
        transform: translateY(-0.5px) scale(1.02);
        background-color: var(--white);
        box-shadow: 0 2px 2px rgba(0, 0, 0, 0.08);
    }

    20% {
        transform: translateY(0) scale(1);
        background-color: var(--white);
        box-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
    }
}

/* --- Additional Maths - Engaging Animated Graph --- */
.icon-add-maths .graph-area {
    width: 60px;
    /* Larger */
    height: 60px;
    border-left: 2px solid var(--grey-medium);
    border-bottom: 2px solid var(--grey-medium);
    position: relative;
    /* Slightly more visible grid */
    background:
        linear-gradient(to right, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px,
        linear-gradient(to bottom, var(--border-light) 1px, transparent 1px) 0 0 / 12px 12px;
    border-radius: 0 0 0 4px;
    /* Rounded corner */
}

.icon-add-maths svg {
    /* Ensure SVG fills the container */
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    overflow: visible;
    /* Allow path to slightly exceed bounds if needed */
}

.icon-add-maths .curve path {
    stroke: var(--math-sci-blue);
    stroke-width: 2.5;
    /* Thicker line */
    fill: none;
    stroke-linecap: round;
    /* Smoother ends */
    /* Adjust dasharray based on actual path length (estimate for now) */
    stroke-dasharray: 150;
    stroke-dashoffset: 150;
    /* More engaging draw/erase animation */
    animation: drawCurveEngage var(--medium-duration) infinite ease-in-out;
    filter: drop-shadow(0 1px 2px rgba(37, 99, 235, 0.3));
    /* Add subtle glow */
}

@keyframes drawCurveEngage {
    0% {
        stroke-dashoffset: 150;
        opacity: 0;
    }

    10% {
        opacity: 1;
    }

    /* Fade in */
    50% {
        stroke-dashoffset: 0;
        opacity: 1;
    }

    /* Drawn */
    60% {
        opacity: 1;
    }

    /* Hold */
    90% {
        opacity: 0;
    }

    /* Fade out */
    100% {
        stroke-dashoffset: -150;
        opacity: 0;
    }

    /* Fully erased */
}

/* Axes labels */
.icon-add-maths::before {
    content: 'y';
    position: absolute;
    top: 2px;
    left: 5px;
    font-size: 11px;
    font-weight: 500;
    color: var(--grey-dark);
}

.icon-add-maths::after {
    content: 'x';
    position: absolute;
    bottom: 2px;
    right: 5px;
    font-size: 11px;
    font-weight: 500;
    color: var(--grey-dark);
}

/* --- Biology - Dynamic DNA Helix --- */
.icon-biology .dna-container {
    width: 35px;
    /* Wider */
    height: 65px;
    position: relative;
    perspective: 500px;
    /* More perspective */
}

.icon-biology .strand-group {
    width: 100%;
    height: 100%;
    position: absolute;
    /* Smoother, slightly faster rotation with subtle X wobble */
    animation: rotateDNADynamic var(--slow-duration) infinite linear;
    transform-style: preserve-3d;
}

.icon-biology .strand {
    position: absolute;
    width: 100%;
    height: 7px;
    /* Thicker */
    border-radius: 3.5px;
    top: 50%;
    margin-top: -3.5px;
    transform-origin: center center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* More distinct strand colors */
.icon-biology .strand.s1 {
    transform: rotateY(0deg) translateZ(14px);
    background: linear-gradient(135deg, var(--bio-chem-green), #047857);
    /* Gradient */
}

.icon-biology .strand.s2 {
    transform: rotateY(180deg) translateZ(14px);
    background: linear-gradient(135deg, var(--bio-chem-green-light), #6EE7B7);
    /* Gradient */
}

/* Base Pairs */
.icon-biology .pair {
    position: absolute;
    width: 22px;
    /* Longer pairs */
    height: 4px;
    /* Thicker pairs */
    border-radius: 2px;
    left: calc(50% - 11px);
    /* Center pairs */
    transform-origin: center center;
    transform: rotateY(90deg) translateZ(0px);
    /* Sync with rotation, add subtle pulse */
    animation: twistPairDynamic var(--slow-duration) infinite linear;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Stagger pairs and alternate colors with gradients */
.icon-biology .pair:nth-child(1) {
    top: 10%;
    animation-delay: 0s;
    background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

.icon-biology .pair:nth-child(2) {
    top: 30%;
    animation-delay: -0.5s;
    background: linear-gradient(90deg, var(--math-sci-blue), #60A5FA);
}

.icon-biology .pair:nth-child(3) {
    top: 50%;
    animation-delay: -1.0s;
    background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

.icon-biology .pair:nth-child(4) {
    top: 70%;
    animation-delay: -1.5s;
    background: linear-gradient(90deg, var(--math-sci-blue), #60A5FA);
}

.icon-biology .pair:nth-child(5) {
    top: 90%;
    animation-delay: -2.0s;
    background: linear-gradient(90deg, var(--accent-pink), #F472B6);
}

@keyframes rotateDNADynamic {
    0% {
        transform: rotateY(0deg) rotateX(8deg);
    }

    /* Slightly more X rotation */
    50% {
        transform: rotateY(180deg) rotateX(-8deg);
    }

    /* Wobble */
    100% {
        transform: rotateY(360deg) rotateX(8deg);
    }
}

@keyframes twistPairDynamic {
    0% {
        transform: rotateY(90deg) translateZ(0px) scale(1);
        opacity: 0.9;
    }

    50% {
        transform: rotateY(270deg) translateZ(0px) scale(1.05);
        opacity: 1;
    }

    /* Subtle pulse */
    100% {
        transform: rotateY(450deg) translateZ(0px) scale(1);
        opacity: 0.9;
    }
}

/* --- Business Studies - Impactful Bar Chart --- */
.icon-business .chart-container {
    width: 65px;
    /* Wider */
    height: 55px;
    display: flex;
    align-items: flex-end;
    justify-content: space-around;
    border-bottom: 2px solid var(--grey-medium);
    position: relative;
    padding: 0 6px;
}

.icon-business .bar {
    width: 13px;
    /* Wider bars */
    background: linear-gradient(to top, var(--business-econ-orange), #FBBF24);
    /* Gradient */
    border-radius: 4px 4px 0 0;
    /* More rounded */
    /* More impactful growth with overshoot */
    animation: growBarImpact var(--medium-duration) infinite ease-out;
    /* Use ease-out for overshoot */
    position: relative;
    box-shadow: inset 0 -3px 5px rgba(0, 0, 0, 0.08);
    /* Deeper inset shadow */
    transform-origin: bottom center;
    /* Grow from bottom */
}

/* Sequential animation with varied heights */
.icon-business .bar:nth-child(1) {
    height: 30px;
    animation-delay: 0s;
}

.icon-business .bar:nth-child(2) {
    height: 50px;
    animation-delay: 0.15s;
    background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

/* Lighter gradient */
.icon-business .bar:nth-child(3) {
    height: 40px;
    animation-delay: 0.3s;
}

.icon-business .bar:nth-child(4) {
    height: 25px;
    animation-delay: 0.45s;
    background: linear-gradient(to top, var(--business-econ-orange-light), #FEF9C3);
}

@keyframes growBarImpact {
    0% {
        transform: scaleY(0);
        opacity: 0;
    }

    60% {
        transform: scaleY(1.1);
        opacity: 1;
    }

    /* Overshoot */
    80% {
        transform: scaleY(0.95);
    }

    /* Bounce back */
    100% {
        transform: scaleY(1);
        opacity: 1;
    }
}

/* --- Chemistry - Lively Beaker --- */
.icon-chemistry .beaker-container {
    width: 50px;
    /* Wider */
    height: 60px;
    position: relative;
    filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.07));
    /* Enhanced shadow */
}

.icon-chemistry .beaker-glass {
    width: 100%;
    height: 100%;
    border: 2.5px solid var(--grey-medium);
    /* Thicker border */
    border-top: none;
    border-radius: 0 0 25px 25px;
    /* More pronounced curve */
    position: absolute;
    bottom: 0;
    /* More vibrant gradient */
    background: linear-gradient(to top, var(--bio-chem-green-light) 10%, rgba(255, 255, 255, 0.5) 90%);
    overflow: hidden;
    z-index: 1;
    box-shadow: inset -3px -3px 6px rgba(0, 0, 0, 0.06);
    /* Enhanced inset */
}

.icon-chemistry .beaker-top {
    width: 112%;
    /* Wider top */
    height: 10px;
    /* Thicker top */
    border: 2.5px solid var(--grey-medium);
    border-radius: 6px 6px 0 0;
    /* Rounded top */
    position: absolute;
    top: -6px;
    /* Adjusted position */
    left: -6%;
    background: var(--white);
    z-index: 2;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.05);
    /* Shadow for top lip */
}

.icon-chemistry .liquid-surface {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70%;
    /* Higher liquid level */
    overflow: hidden;
    border-radius: 0 0 22px 22px;
    /* Match inner radius */
    background-color: var(--bio-chem-green);
    /* Solid color base */
}

.icon-chemistry .liquid-surface::after {
    /* More dynamic wavy surface */
    content: '';
    position: absolute;
    width: 180%;
    height: 12px;
    /* Larger wave */
    background: var(--bio-chem-green-light);
    /* Lighter color for wave */
    border-radius: 45% 55% 40% 60%;
    /* Irregular shape */
    top: -4px;
    left: -40%;
    /* Faster, more noticeable wave */
    animation: waveSurfaceLively var(--medium-duration) infinite linear alternate;
    opacity: 0.8;
    filter: blur(1px);
}

@keyframes waveSurfaceLively {
    0% {
        transform: translateX(0) rotate(-1deg);
        border-radius: 45% 55% 40% 60%;
    }

    100% {
        transform: translateX(-10px) rotate(1deg);
        border-radius: 55% 45% 60% 40%;
    }
}

.icon-chemistry .bubble {
    position: absolute;
    bottom: 8px;
    /* Start lower */
    width: 6px;
    height: 6px;
    /* Base size */
    background-color: rgba(255, 255, 255, 0.85);
    /* More opaque */
    border-radius: 50%;
    /* More varied and lively bubble animation */
    animation: bubbleUpLively var(--fast-duration) infinite ease-in;
    z-index: 3;
    filter: blur(0.5px);
    opacity: 0;
    /* Start hidden */
}

/* Varied sizes and delays */
.icon-chemistry .bubble:nth-child(1) {
    left: 25%;
    animation-delay: 0s;
    width: 5px;
    height: 5px;
}

.icon-chemistry .bubble:nth-child(2) {
    left: 70%;
    animation-delay: 0.6s;
    width: 7px;
    height: 7px;
}

.icon-chemistry .bubble:nth-child(3) {
    left: 45%;
    animation-delay: 1.2s;
    width: 6px;
    height: 6px;
}

.icon-chemistry .bubble:nth-child(4) {
    left: 55%;
    animation-delay: 1.8s;
    width: 4px;
    height: 4px;
}

/* Extra small bubble */

@keyframes bubbleUpLively {
    0% {
        bottom: 8px;
        opacity: 0;
        transform: scale(0.4) translateX(0);
    }

    20% {
        opacity: 0.9;
        transform: scale(0.8);
    }

    /* Appear and grow */
    80% {
        opacity: 0.2;
        transform: scale(1.1) translateX(3px);
    }

    /* Grow, fade, slight sideways move */
    95% {
        opacity: 0.1;
        transform: scale(1.15) translateX(-2px);
    }

    /* Wobble */
    100% {
        bottom: 75%;
        opacity: 0;
        transform: scale(1.2) translateX(0);
    }

    /* Rise higher before disappearing */
}

/* --- Computer Science - Realistic Terminal --- */
.icon-cs .terminal-window {
    width: 65px;
    height: 50px;
    background-color: #1a202c;
    border-radius: 8px;
    padding: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border: 1px solid #2d3748;
    transform-origin: center;
    animation: terminalPulse 4s infinite ease-in-out;
}

@keyframes terminalPulse {

    0%,
    100% {
        transform: scale(1);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }

    50% {
        transform: scale(1.02);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25);
    }
}

.icon-cs .window-buttons {
    position: absolute;
    top: 7px;
    left: 10px;
    display: flex;
    gap: 6px;
    /* Increased gap */
}

.icon-cs .window-button {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.icon-cs .btn-red {
    background-color: #ff5f57;
}

.icon-cs .btn-yellow {
    background-color: #ffbd2e;
}

.icon-cs .btn-green {
    background-color: #28c940;
}

.icon-cs .code-area {
    position: absolute;
    top: 22px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    font-family: 'Menlo', 'Courier New', Courier, monospace;
    font-size: 8px;
    color: #A0AEC0;
    line-height: 1.4;
    overflow: hidden;
}

.icon-cs .code-line {
    white-space: pre;
    position: relative;
    height: 1.4em;
    color: #E2E8F0;
}

.icon-cs .code-line span {
    opacity: 0;
    animation: typeChar 0.1s forwards;
    color: inherit;
}

.icon-cs .code-line:nth-child(1) {
    animation-delay: 0.5s;
    color: #68D391;
}

.icon-cs .code-line:nth-child(2) {
    animation-delay: 2s;
    color: #4299E1;
}

.icon-cs .code-line:nth-child(3) {
    animation-delay: 3.5s;
    color: #F687B3;
}

.icon-cs .code-line:nth-child(1) span {
    animation-delay: calc(0.5s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(2) span {
    animation-delay: calc(2s + (var(--char-index) * 0.05s));
}

.icon-cs .code-line:nth-child(3) span {
    animation-delay: calc(3.5s + (var(--char-index) * 0.05s));
}

@keyframes typeChar {
    from {
        opacity: 0;
        transform: translateY(1px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.icon-cs .cursor {
    width: 5px;
    height: 12px;
    background-color: #48BB78;
    position: absolute;
    top: 1px;
    animation: blink 1s infinite steps(2, start);
}

@keyframes blink {
    to {
        visibility: hidden;
    }
}



/* Keyframes need careful coordination with HTML content */
@keyframes typeLineRealistic {

    /* This animation reveals spans sequentially */
    /* Timings need to match cursor movement */
    0%,
    100% {
        opacity: 0;
    }

    /* Example timing for revealing first line */
    2% {
        opacity: 1;
    }

    /* Reveal '$' */
    4% {
        opacity: 1;
    }

    /* Reveal ' ' */
    6% {
        opacity: 1;
    }

    /* Reveal 'n' */
    8% {
        opacity: 1;
    }

    /* Reveal 'p' */
    10% {
        opacity: 1;
    }

    /* Reveal 'm' */
    /* ... and so on ... */
    /* Fade out before next line */
    30% {
        opacity: 0;
    }

    /* Example timing for second line */
    32% {
        opacity: 1;
    }

    /* Reveal '$' */
    /* ... */
    60% {
        opacity: 0;
    }

    /* Example timing for third line */
    62% {
        opacity: 1;
    }

    /* Reveal '$' */
    /* ... */
    90% {
        opacity: 0;
    }
}

@keyframes blinkAndMoveCursorRealistic {

    /* Blink */
    0%,
    48%,
    100% {
        opacity: 1;
    }

    50%,
    98% {
        opacity: 0;
    }

    49%,
    99% {
        opacity: 1;
    }

    /* Movement - coordinated with typing animation and HTML structure */
    /* Line 1 */
    0% {
        top: 0em;
        left: 0ch;
    }

    /* Start of line 1 */
    2% {
        top: 0em;
        left: 1ch;
    }

    /* After '$' */
    4% {
        top: 0em;
        left: 2ch;
    }

    /* After ' ' */
    6% {
        top: 0em;
        left: 3ch;
    }

    /* After 'n' */
    8% {
        top: 0em;
        left: 4ch;
    }

    /* After 'p' */
    10% {
        top: 0em;
        left: 5ch;
    }

    /* After 'm' */
    12% {
        top: 0em;
        left: 6ch;
    }

    /* After ' ' */
    14% {
        top: 0em;
        left: 7ch;
    }

    /* After 'i' */
    16% {
        top: 0em;
        left: 8ch;
    }

    /* After 'n' */
    18% {
        top: 0em;
        left: 9ch;
    }

    /* After 's' */
    20% {
        top: 0em;
        left: 10ch;
    }

    /* After 't' */
    22% {
        top: 0em;
        left: 11ch;
    }

    /* After 'a' */
    24% {
        top: 0em;
        left: 12ch;
    }

    /* After 'l' */
    26% {
        top: 0em;
        left: 13ch;
    }

    /* After 'l' */
    /* Pause */
    30% {
        top: 1.5em;
        left: 0ch;
    }

    /* Start of line 2 */
    /* Line 2 */
    32% {
        top: 1.5em;
        left: 1ch;
    }

    /* After '$' */
    34% {
        top: 1.5em;
        left: 2ch;
    }

    /* After ' ' */
    36% {
        top: 1.5em;
        left: 3ch;
    }

    /* After 'c' */
    38% {
        top: 1.5em;
        left: 4ch;
    }

    /* After 'd' */
    40% {
        top: 1.5em;
        left: 5ch;
    }

    /* After ' ' */
    42% {
        top: 1.5em;
        left: 6ch;
    }

    /* After '.' */
    44% {
        top: 1.5em;
        left: 7ch;
    }

    /* After '.' */
    /* Pause */
    60% {
        top: 3em;
        left: 0ch;
    }

    /* Start of line 3 */
    /* Line 3 */
    62% {
        top: 3em;
        left: 1ch;
    }

    /* After '$' */
    64% {
        top: 3em;
        left: 2ch;
    }

    /* After ' ' */
    66% {
        top: 3em;
        left: 3ch;
    }

    /* After 'l' */
    68% {
        top: 3em;
        left: 4ch;
    }

    /* After 's' */
    /* Pause */
    90% {
        top: 0em;
        left: 0ch;
    }

    /* Reset */
}


/* --- Economics - Enhanced Dynamic Dollar --- */
.icon-economics .chart-container {
    width: 60px;
    height: 60px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: visible;
    /* Allow coins to float outside */
}

.icon-economics .dollar-symbol {
    position: relative;
    font-size: 40px;
    /* Slightly smaller to make room for coins */
    font-weight: bold;
    color: var(--business-econ-orange);
    /* More dynamic pulse and glow */
    text-shadow:
        0 0 10px rgba(245, 158, 11, 0.6),
        /* Brighter base glow */
        0 0 20px rgba(245, 158, 11, 0.4);
    animation: pulseDollarEnhanced var(--medium-duration) infinite ease-in-out;
    z-index: 2;
}

.icon-economics .dollar-symbol::before {
    content: '$';
}

.icon-economics .glow-circle {
    position: absolute;
    width: 45px;
    /* Slightly smaller glow */
    height: 45px;
    border-radius: 50%;
    /* Refined radial glow */
    background: radial-gradient(circle, rgba(254, 243, 199, 0.7) 0%, rgba(254, 243, 199, 0.1) 50%, transparent 70%);
    animation: pulseGlowEnhanced var(--medium-duration) infinite alternate;
    z-index: 1;
}

@keyframes pulseDollarEnhanced {
    0% {
        transform: scale(1);
        color: var(--business-econ-orange);
        text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
    }

    50% {
        transform: scale(1.12);
        /* Slightly larger pulse */
        color: #FBBF24;
        /* Brighter yellow */
        text-shadow: 0 0 18px rgba(245, 158, 11, 0.8), 0 0 35px rgba(245, 158, 11, 0.6);
        /* Stronger glow */
    }

    100% {
        transform: scale(1);
        color: var(--business-econ-orange);
        text-shadow: 0 0 10px rgba(245, 158, 11, 0.6), 0 0 20px rgba(245, 158, 11, 0.4);
    }
}

@keyframes pulseGlowEnhanced {
    0% {
        transform: scale(0.9);
        opacity: 0.4;
        /* Slightly more visible base */
    }

    100% {
        transform: scale(1.15);
        /* Larger glow pulse */
        opacity: 0.7;
        /* Brighter peak */
    }
}

/* Enhanced floating coins */
.icon-economics .coin {
    position: absolute;
    width: 14px;
    height: 14px;
    /* Realistic gold gradient */
    background: linear-gradient(135deg, #FCD34D, #FBBF24 40%, #D97706);
    border-radius: 50%;
    /* Darker, sharper border */
    border: 1.5px solid #92400E;
    /* Enhanced shadow for depth and shine */
    box-shadow:
        inset 0 -1px 2px rgba(0, 0, 0, 0.25),
        /* Inner bottom shadow */
        inset 0 1px 1px rgba(255, 255, 255, 0.3),
        /* Inner top highlight */
        0 3px 5px rgba(0, 0, 0, 0.2),
        /* Drop shadow */
        0 0 8px rgba(245, 158, 11, 0.5);
    /* Outer glow */
    opacity: 0;
    /* More complex float/spin animation */
    animation: floatCoinEnhanced var(--medium-duration) infinite ease-out;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 8px;
    font-weight: bold;
    color: #92400E;
    /* Dark brown for '$' */
    z-index: 3;
}

.icon-economics .coin::before {
    content: '$';
    display: block;
    /* Needed for transform */
    /* Ensure '$' rotates with the coin */
    animation: inherit;
    /* Use parent animation timing */
    animation-name: rotateCoinSymbol;
}

@keyframes floatCoinEnhanced {
    0% {
        transform: translateY(25px) rotateY(0deg) scale(0.4) rotateZ(0deg);
        opacity: 0;
    }

    15% {
        /* Appear and start rising/spinning */
        opacity: 0.9;
        transform: translateY(10px) rotateY(180deg) scale(1) rotateZ(45deg);
    }

    70% {
        /* Float higher, continue spinning/rotating */
        opacity: 0.9;
        transform: translateY(-35px) rotateY(540deg) scale(1) rotateZ(270deg);
    }

    100% {
        /* Float out of view */
        transform: translateY(-55px) rotateY(720deg) scale(0.4) rotateZ(360deg);
        opacity: 0;
    }
}

/* Separate animation for the '$' symbol to counter the Y rotation */
@keyframes rotateCoinSymbol {
    0% {
        transform: rotateY(0deg) rotateZ(0deg);
    }

    15% {
        transform: rotateY(-180deg) rotateZ(-45deg);
    }

    70% {
        transform: rotateY(-540deg) rotateZ(-270deg);
    }

    100% {
        transform: rotateY(-720deg) rotateZ(-360deg);
    }
}


/* Staggered coins with varied sizes and more central positions */
.icon-economics .coin1 {
    bottom: 5px;
    left: -10px;
    /* Original edge */
    animation-delay: 0.2s;
}

.icon-economics .coin2 {
    bottom: 15px;
    right: -10px;
    /* Original edge */
    animation-delay: 0.8s;
    width: 12px;
    height: 12px;
    font-size: 7px;
}

.icon-economics .coin3 {
    bottom: 30px;
    left: 5px;
    /* Closer to center */
    animation-delay: 1.4s;
    width: 10px;
    height: 10px;
    font-size: 6px;
}

.icon-economics .coin4 {
    bottom: 25px;
    right: 5px;
    /* Closer to center */
    animation-delay: 2.0s;
    width: 11px;
    height: 11px;
    font-size: 6.5px;
}

/* Added more coins for better fill */
.icon-economics .coin5 {
    bottom: 5px;
    right: 15px;
    /* More central */
    animation-delay: 0.5s;
    width: 13px;
    height: 13px;
    font-size: 7.5px;
}

.icon-economics .coin6 {
    bottom: 40px;
    left: 20px;
    /* Higher and central */
    animation-delay: 1.1s;
    width: 9px;
    height: 9px;
    font-size: 5.5px;
}

.icon-economics .coin7 {
    bottom: 10px;
    left: 25px;
    /* Lower central */
    animation-delay: 1.7s;
    width: 12px;
    height: 12px;
    font-size: 7px;
}


/* --- Geography - Premium 3D Globe --- */
.icon-wrapper .icon-geography {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.icon-wrapper .icon-geography .globe-container {
    width: 80px;
    height: 80px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    perspective: 1200px;
    transform-style: preserve-3d;
}

/* --- 3D Globe Sphere --- */
.icon-wrapper .icon-geography .globe-sphere {
    width: 65px;
    height: 65px;
    background: radial-gradient(
        circle at 30% 30%,
        #b6faff 0%,
        #4defff 30%,
        #0284c7 60%,
        #0c4a6e 90%
    );
    border-radius: 50%;
    position: relative;
    transform-style: preserve-3d;
    animation: rotateGlobe3D 12s infinite linear;
    box-shadow: 
        inset -15px -12px 30px rgba(0, 0, 0, 0.4),
        inset 10px 10px 25px rgba(255, 255, 255, 0.2),
        0 0 40px rgba(76, 222, 255, 0.4),
        0 10px 30px rgba(0, 0, 0, 0.3);
    overflow: visible;
}

/* --- Continent Shapes with 3D Relief --- */
.icon-wrapper .icon-geography .continent {
    position: absolute;
    border-radius: 50% 50% 45% 45% / 60% 65% 40% 45%;
    transform-style: preserve-3d;
    box-shadow: 
        1px 1px 8px rgba(0, 0, 0, 0.3),
        inset 0 0 10px rgba(0, 0, 0, 0.1);
    animation: continentPulse 8s infinite alternate ease-in-out;
}

.icon-wrapper .icon-geography .c1 {
    /* Americas */
    top: 15%;
    left: 10%;
    width: 38%;
    height: 40%;
    background: linear-gradient(145deg, #34D399, #10B981 60%, #059669);
    transform: rotate(-20deg) translateZ(2px);
    animation-delay: -0.5s;
}

.icon-wrapper .icon-geography .c2 {
    /* Africa/Europe */
    top: 25%;
    right: 15%;
    width: 30%;
    height: 35%;
    background: linear-gradient(135deg, #6EE7B7, #34D399 60%, #10B981);
    transform: rotate(15deg) translateZ(3px);
    animation-delay: -1.5s;
}

.icon-wrapper .icon-geography .c3 {
    /* Asia/Australia */
    bottom: 15%;
    left: 30%;
    width: 35%;
    height: 25%;
    background: linear-gradient(135deg, #059669, #047857);
    transform: rotate(5deg) translateZ(2.5px);
    animation-delay: -2.5s;
}

/* --- 3D Grid System --- */
.icon-wrapper .icon-geography .grid-line {
    position: absolute;
    background: linear-gradient(90deg, 
        rgba(76, 222, 255, 0.3) 0%, 
        rgba(255, 255, 255, 0.5) 50%, 
        rgba(76, 222, 255, 0.3) 100%);
    z-index: 2;
    border-radius: 50%;
    pointer-events: none;
    filter: blur(0.5px);
    animation: gridPulse 5s infinite alternate ease-in-out;
    transform-style: preserve-3d;
}

/* Latitude Lines */
.icon-wrapper .icon-geography .grid-line-lat1 {
    top: 20%;
    width: 100%;
    height: 1.5px;
    transform: rotateX(75deg) scaleY(0.8) translateZ(5px);
}

.icon-wrapper .icon-geography .grid-line-lat2 {
    top: 50%;
    width: 100%;
    height: 2px;
    transform: rotateX(0deg) scaleY(1) translateZ(10px);
}

.icon-wrapper .icon-geography .grid-line-lat3 {
    top: 80%;
    width: 100%;
    height: 1.5px;
    transform: rotateX(-75deg) scaleY(0.8) translateZ(5px);
}

/* Longitude Lines */
.icon-wrapper .icon-geography .grid-line-lon1 {
    left: 25%;
    height: 100%;
    width: 1.5px;
    transform: rotateY(70deg) scaleX(0.6) translateZ(8px);
}

.icon-wrapper .icon-geography .grid-line-lon2 {
    left: 50%;
    height: 100%;
    width: 2px;
    transform: rotateY(0deg) scaleX(1) translateZ(10px);
}

.icon-wrapper .icon-geography .grid-line-lon3 {
    left: 75%;
    height: 100%;
    width: 1.5px;
    transform: rotateY(-70deg) scaleX(0.6) translateZ(8px);
}

/* --- Orbiting Satellites --- */
.icon-wrapper .icon-geography .orbit {
    position: absolute;
    top: 50%;
    left: 50%;
    border: 1px dashed rgba(76, 222, 255, 0.5);
    border-radius: 50%;
    transform-style: preserve-3d;
    pointer-events: none;
    animation: orbitSpin 15s infinite linear;
}

.icon-wrapper .icon-geography .orbit1 {
    width: 90px;
    height: 90px;
    transform: translate(-50%, -50%) rotateX(65deg);
    animation-duration: 20s;
}

.icon-wrapper .icon-geography .orbit2 {
    width: 75px;
    height: 75px;
    transform: translate(-50%, -50%) rotateX(25deg) rotateY(30deg);
    animation-duration: 15s;
    animation-direction: reverse;
}

.icon-wrapper .icon-geography .satellite {
    position: absolute;
    width: 6px;
    height: 3px;
    background: linear-gradient(90deg, #d1d5db, #9ca3af);
    border-radius: 2px;
    top: 50%;
    left: 0;
    margin-top: -1.5px;
    transform-origin: center center;
    transform-style: preserve-3d;
    animation: 
        satelliteOrbit 15s infinite linear,
        satellitePulse 3s infinite ease-in-out;
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
}

.icon-wrapper .icon-geography .orbit1 .satellite {
    animation-duration: 20s;
    transform: rotate(45deg);
}

.icon-wrapper .icon-geography .orbit2 .satellite {
    animation-duration: 15s;
    animation-direction: reverse;
    transform: rotate(-30deg);
}

/* --- Atmospheric Glow --- */
.icon-wrapper .icon-geography .atmosphere {
    position: absolute;
    width: 75px;
    height: 75px;
    border-radius: 50%;
    background: radial-gradient(
        circle at 30% 30%,
        rgba(76, 222, 255, 0.1) 0%,
        rgba(76, 222, 255, 0.05) 50%,
        transparent 70%
    );
    animation: atmospherePulse 8s infinite alternate ease-in-out;
}

/* --- Cloud Effects --- */
.icon-wrapper .icon-geography .cloud {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    filter: blur(1px);
    opacity: 0.7;
    animation: cloudMove 30s infinite linear;
    transform-style: preserve-3d;
}

.icon-wrapper .icon-geography .cloud1 {
    width: 15px;
    height: 8px;
    top: 20%;
    left: 30%;
    animation-duration: 25s;
}

.icon-wrapper .icon-geography .cloud2 {
    width: 20px;
    height: 10px;
    top: 40%;
    left: 60%;
    animation-duration: 30s;
}

.icon-wrapper .icon-geography .cloud3 {
    width: 12px;
    height: 6px;
    top: 60%;
    left: 20%;
    animation-duration: 35s;
}

/* --- Stand with Metallic Finish --- */
.icon-wrapper .icon-geography .globe-stand {
    width: 28px;
    height: 20px;
    background: linear-gradient(
        135deg,
        #e5e7eb,
        #d1d5db 30%,
        #9ca3af 70%,
        #6b7280
    );
    border-radius: 5px 5px 3px 3px;
    position: absolute;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%) translateZ(-10px);
    box-shadow: 
        0 6px 12px rgba(0, 0, 0, 0.3),
        inset 0 3px 3px rgba(255, 255, 255, 0.4);
    z-index: -1;
}

/* --- Geography Animations (scoped with unique names) --- */
@keyframes rotateGlobe3D {
    0% {
        transform: rotateX(15deg) rotateY(0deg) rotateZ(-3deg);
    }
    25% {
        transform: rotateX(19deg) rotateY(90deg) rotateZ(1deg);
    }
    50% {
        transform: rotateX(14deg) rotateY(180deg) rotateZ(3deg);
    }
    75% {
        transform: rotateX(17deg) rotateY(270deg) rotateZ(-1deg);
    }
    100% {
        transform: rotateX(15deg) rotateY(360deg) rotateZ(-3deg);
    }
}

@keyframes continentPulse {
    0% {
        transform: translateZ(1px) scale(1);
        filter: brightness(0.95) blur(0.5px);
    }
    100% {
        transform: translateZ(3px) scale(1.05);
        filter: brightness(1.1) blur(0.5px);
    }
}

@keyframes gridPulse {
    0% {
        opacity: 0.2;
        box-shadow: 0 0 2px rgba(255, 255, 255, 0.2);
    }
    100% {
        opacity: 0.4;
        box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
    }
}

@keyframes orbitSpin {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes satelliteOrbit {
    from {
        transform: rotate(0deg) translateX(45px) rotate(0deg);
    }
    to {
        transform: rotate(360deg) translateX(45px) rotate(360deg);
    }
}

@keyframes satellitePulse {
    0%, 100% {
        opacity: 0.8;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}

@keyframes atmospherePulse {
    0% {
        transform: scale(1);
        opacity: 0.5;
    }
    100% {
        transform: scale(1.1);
        opacity: 0.8;
    }
}

@keyframes cloudMove {
    0% {
        transform: translateX(0) translateY(0) translateZ(5px);
    }
    25% {
        transform: translateX(-10px) translateY(5px) translateZ(5px);
    }
    50% {
        transform: translateX(0) translateY(10px) translateZ(5px);
    }
    75% {
        transform: translateX(10px) translateY(5px) translateZ(5px);
    }
    100% {
        transform: translateX(0) translateY(0) translateZ(5px);
    }
}

/* --- Innovative Orbit for Geography Icon --- */
.icon-geography .globe-orbit {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    box-shadow: 0 0 22px 6px #4defff44, 0 0 0 2px #1d293a;
    border: 2px dashed #4defff88;
    animation: orbitSpin 5s linear infinite;
    z-index: 1;
    pointer-events: none;
}

@keyframes orbitSpin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* --- Ping Effect --- */
.icon-geography .globe-ping {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 24px;
    height: 24px;
    background: radial-gradient(circle, #4defffcc 60%, transparent 100%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.7;
    animation: pingPulse 2.5s cubic-bezier(0.4,0,0.2,1) infinite;
    z-index: 4;
    pointer-events: none;
}

@keyframes pingPulse {
    0% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.8); }
    60% { opacity: 0.2; transform: translate(-50%, -50%) scale(1.7); }
    100% { opacity: 0; transform: translate(-50%, -50%) scale(2); }
}

/* --- Enhanced Globe Rotation: Add ease and slight speed variation --- */
.icon-geography .globe-sphere {
    animation: rotateGlobeDynamic 7.5s linear infinite;
}

@keyframes rotateGlobeDynamic {
    0% { transform: translateX(-50%) rotateX(15deg) rotateY(0deg) rotateZ(-3deg); }
    15% { transform: translateX(-50%) rotateX(19deg) rotateY(60deg) rotateZ(1deg); }
    35% { transform: translateX(-50%) rotateX(17deg) rotateY(120deg) rotateZ(-2deg); }
    55% { transform: translateX(-50%) rotateX(14deg) rotateY(180deg) rotateZ(3deg); }
    75% { transform: translateX(-50%) rotateX(17deg) rotateY(270deg) rotateZ(-1deg); }
    100% { transform: translateX(-50%) rotateX(15deg) rotateY(360deg) rotateZ(-3deg); }
}


.icon-geography .globe-stand {
    width: 24px;
    /* Slightly wider */
    height: 18px;
    /* Taller stand */
    /* More refined metallic gradient */
    background: linear-gradient(135deg, #D1D5DB, var(--grey-medium) 50%, var(--grey-dark));
    border-radius: 5px 5px 3px 3px;
    /* Smoother rounding */
    position: absolute;
    bottom: 0px;
    /* Position at the very bottom */
    left: 50%;
    transform: translateX(-50%) translateZ(-5px);
    /* Push stand back slightly */
    /* Enhanced shadow and highlight for depth */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.25), inset 0 2px 2px rgba(255, 255, 255, 0.3);
    z-index: 1;
}

.icon-geography .globe-sphere {
    width: 60px;
    height: 60px;
    /* Keep sphere size */
    /* Enhanced 3D globe with more realistic ocean gradient */
    background: radial-gradient(circle at 35% 30%,
            /* Shift highlight */
            #ADDBF3,
            /* Lighter blue highlight */
            #38BDF8,
            /* Mid blue */
            #0284C7 70%,
            /* Deeper blue */
            #0369A1 90%);
    /* Darkest edge */
    border-radius: 50%;
    position: absolute;
    bottom: 10px;
    /* Adjust vertical position relative to stand */
    left: 50%;
    transform-origin: center center;
    /* Initial transform set within animation */
    overflow: hidden;
    /* Enhanced 3D shadow effects */
    box-shadow:
        inset -12px -10px 25px rgba(0, 0, 0, 0.35),
        /* Stronger inner shadow */
        inset 8px 8px 20px rgba(255, 255, 255, 0.15),
        /* Inner highlight */
        0 0 25px rgba(56, 189, 248, 0.3),
        /* Outer glow (sky blue) */
        0 6px 18px rgba(0, 0, 0, 0.25);
    /* Drop shadow */
    /* Smoother 3D rotation animation */
    animation: rotateGlobe3D var(--slow-duration) infinite linear;
    /* Slower, smoother rotation */
    transform-style: preserve-3d;
    backface-visibility: hidden;
    /* Prevent flickering during rotation */
    z-index: 2;
}

/* Enhanced grid lines for latitude/longitude */
.icon-geography .grid-line {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.2);
    /* Slightly less opaque */
    z-index: 3;
    /* Above continents */
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.25);
    /* Subtle glow */
    /* Subtle pulse animation */
    animation: gridPulse var(--medium-duration) infinite alternate ease-in-out;
    transform-style: preserve-3d;
    /* Important for perspective */
}

@keyframes gridPulse {
    0% {
        opacity: 0.2;
        box-shadow: 0 0 2px rgba(255, 255, 255, 0.2);
    }

    100% {
        opacity: 0.4;
        box-shadow: 0 0 5px rgba(255, 255, 255, 0.35);
    }
}

/* Horizontal grid lines (latitude) - Adjusted for perspective */
.icon-geography .grid-line-lat {
    width: 100%;
    height: 1px;
    left: 0;
    border-radius: 50%;
    /* Make them curve slightly */
    transform-origin: center center;
}

.icon-geography .grid-line-lat1 {
    top: 25%;
    transform: rotateX(75deg) scaleY(0.8) translateZ(5px);
}

/* More curve */
.icon-geography .grid-line-lat2 {
    top: 50%;
    transform: rotateX(0deg) scaleY(1) translateZ(10px);
}

/* Equator - push forward */
.icon-geography .grid-line-lat3 {
    top: 75%;
    transform: rotateX(-75deg) scaleY(0.8) translateZ(5px);
}

/* Vertical grid lines (longitude) - Adjusted for perspective */
.icon-geography .grid-line-lon {
    width: 1px;
    height: 100%;
    top: 0;
    border-radius: 50%;
    transform-origin: center center;
}

.icon-geography .grid-line-lon1 {
    left: 25%;
    transform: rotateY(70deg) scaleX(0.6) translateZ(8px);
}

.icon-geography .grid-line-lon2 {
    left: 50%;
    transform: rotateY(0deg) scaleX(1) translateZ(10px);
}

/* Prime meridian */
.icon-geography .grid-line-lon3 {
    left: 75%;
    transform: rotateY(-70deg) scaleX(0.6) translateZ(8px);
}

/* Enhanced 3D continents */
.icon-geography .continent {
    position: absolute;
    /* More realistic land colors with gradient */
    background: linear-gradient(145deg, #34D399, #10B981 60%, #059669);
    /* Greener gradient */
    border-radius: 45% 55% 50% 50% / 60% 65% 40% 45%;
    /* More organic shapes */
    opacity: 0.85;
    /* More visible */
    filter: blur(0.5px);
    /* Slightly blurred edges */
    /* Add 3D elevation effect */
    transform-style: preserve-3d;
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    /* Subtle shadow */
    /* Subtle pulse/breathing effect */
    animation: continentPulse var(--medium-duration) infinite alternate ease-in-out;
    z-index: 2;
    /* Below grid lines */
}

@keyframes continentPulse {
    0% {
        transform: translateZ(1px) scale(1);
        filter: brightness(0.95) blur(0.5px);
    }

    100% {
        transform: translateZ(2.5px) scale(1.03);
        filter: brightness(1.1) blur(0.5px);
    }

    /* Lift and brighten */
}

/* Adjusted continent positions and 3D transforms (relative to sphere) */
/* Note: These transforms are applied *in addition* to the globe's rotation */
.icon-geography .c1 {
    /* Approx North/South America */
    top: 15%;
    left: 10%;
    width: 38%;
    height: 40%;
    transform: rotate(-20deg) translateZ(2px);
    /* More elevation */
    animation-delay: -0.5s;
}

.icon-geography .c2 {
    /* Approx Africa/Europe */
    top: 25%;
    right: 15%;
    width: 30%;
    height: 35%;
    transform: rotate(15deg) translateZ(3px);
    /* More elevation */
    background: linear-gradient(135deg, #6EE7B7, #34D399 60%, #10B981);
    /* Different green */
    animation-delay: -1.5s;
}

.icon-geography .c3 {
    /* Approx Asia/Australia */
    bottom: 15%;
    left: 30%;
    width: 35%;
    height: 25%;
    transform: rotate(5deg) translateZ(2.5px);
    animation-delay: -2.5s;
}

.icon-geography .c4 {
    /* Small island */
    top: 60%;
    right: 10%;
    width: 15%;
    height: 12%;
    transform: rotate(-10deg) translateZ(2px);
    background: linear-gradient(135deg, #059669, #047857);
    /* Darker green */
    border-radius: 60% 40% 50% 50% / 50% 50% 50% 50%;
    /* Rounder */
    animation-delay: -3.5s;
}

/* Enhanced 3D rotation animation with more variation */
@keyframes rotateGlobe3D {
    0% {
        transform: translateX(-50%) rotateX(15deg) rotateY(0deg) rotateZ(-3deg);
    }

    25% {
        transform: translateX(-50%) rotateX(19deg) rotateY(90deg) rotateZ(1deg);
    }

    /* Increased X tilt */
    50% {
        transform: translateX(-50%) rotateX(14deg) rotateY(180deg) rotateZ(3deg);
    }

    /* Opposite Z tilt */
    75% {
        transform: translateX(-50%) rotateX(17deg) rotateY(270deg) rotateZ(-1deg);
    }

    /* Reduced X tilt */
    100% {
        transform: translateX(-50%) rotateX(15deg) rotateY(360deg) rotateZ(-3deg);
    }
}


/* --- History - Elegant Hourglass --- */
.icon-history .hourglass-container {
    width: 40px;
    height: 55px;
    /* Adjusted size */
    position: relative;
    perspective: 350px;
    /* Increased perspective */
    filter: drop-shadow(0 3px 4px rgba(0, 0, 0, 0.08));
}

.icon-history .hourglass-body {
    width: 100%;
    height: 100%;
    position: absolute;
    /* Slower, more graceful flip */
    animation: flipHourglassElegant var(--very-slow-duration) infinite ease-in-out;
    transform-style: preserve-3d;
}

.icon-history .hg-frame {
    position: absolute;
    width: 100%;
    height: 6px;
    /* Thicker frame */
    background: linear-gradient(#A0522D, #8B4513);
    /* Brown gradient */
    border-radius: 3px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.icon-history .hg-top {
    top: 0;
}

.icon-history .hg-bottom {
    bottom: 0;
}

.icon-history .hg-post {
    position: absolute;
    width: 6px;
    height: 100%;
    /* Thicker posts */
    background: linear-gradient(#A0522D, #8B4513);
    top: 0;
}

.icon-history .hg-post-left {
    left: 2px;
    border-radius: 3px 0 0 3px;
}

/* Position closer in */
.icon-history .hg-post-right {
    right: 2px;
    border-radius: 0 3px 3px 0;
}

.icon-history .glass-container {
    position: absolute;
    width: 70%;
    height: 80%;
    /* Adjusted size */
    top: 10%;
    left: 15%;
    transform: translateZ(3px);
    /* Bring glass forward */
}

.icon-history .glass-bulb {
    position: absolute;
    width: 100%;
    height: 50%;
    border: 2.5px solid var(--math-sci-blue-light);
    /* Thicker glass */
    box-sizing: border-box;
    /* Subtle glass reflection */
    background: linear-gradient(to top, rgba(255, 255, 255, 0.02), rgba(255, 255, 255, 0.1));
    overflow: hidden;
    /* Contain sand */
}

.icon-history .glass-top {
    top: 0;
    border-bottom: none;
    border-radius: 15px 15px 5px 5px;
}

/* More bulbous */
.icon-history .glass-bottom {
    bottom: 0;
    border-top: none;
    border-radius: 5px 5px 15px 15px;
}

.icon-history .glass-container::before {
    /* Narrowing */
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4px;
    height: 6px;
    background: var(--math-sci-blue-light);
    /* Match border */
    border-radius: 1px;
}

.icon-history .sand {
    position: absolute;
    background-color: #FCD34D;
    /* Lighter sand color */
    left: 50%;
    transform: translateX(-50%);
    border-radius: 3px;
}

.icon-history .sand-pile-top {
    width: 80%;
    height: 40%;
    /* Adjusted size */
    bottom: 50%;
    border-radius: 0 0 10px 10px;
    /* Rounded bottom */
    animation: sandTopFallElegant var(--very-slow-duration) linear infinite;
    /* Sync */
    background: linear-gradient(to top, #FBBF24, #FCD34D);
    /* Sand gradient */
}

.icon-history .sand-pile-bottom {
    width: 80%;
    height: 0%;
    bottom: 2.5px;
    /* Sit just above bottom glass */
    border-radius: 10px 10px 0 0;
    /* Rounded top */
    animation: sandBottomFillElegant var(--very-slow-duration) linear infinite;
    /* Sync */
    background: linear-gradient(to top, #FBBF24, #FCD34D);
    /* Sand gradient */
}

.icon-history .sand-stream {
    width: 1.5px;
    /* Thicker stream */
    height: 10%;
    top: 45%;
    /* Adjusted position */
    animation: sandStreamFlowElegant var(--very-slow-duration) linear infinite;
    /* Sync */
    background-color: #FCD34D;
    box-shadow: 0 0 3px #FBBF24;
    /* Subtle glow */
}

@keyframes flipHourglassElegant {

    0%,
    45% {
        transform: rotateX(0deg);
    }

    /* Longer wait */
    50% {
        transform: rotateX(180deg);
    }

    /* Flip */
    55%,
    100% {
        transform: rotateX(180deg);
    }

    /* Hold flipped */
    /* Note: The animation needs adjustment if a 360 flip is desired */
}

/* Adjust sand animations to match 10s duration and flip timing */
@keyframes sandTopFallElegant {
    0% {
        height: 40%;
        bottom: 50%;
        opacity: 1;
    }

    45% {
        height: 0%;
        bottom: 50%;
        opacity: 1;
    }

    /* Empty just before flip */
    45.1%,
    50% {
        height: 0%;
        opacity: 0;
    }

    /* Hide during flip */
    50.1% {
        height: 0%;
        bottom: 2.5px;
        opacity: 0;
    }

    /* Appear at bottom */
    55% {
        height: 0%;
        bottom: 2.5px;
        opacity: 1;
    }

    /* Become visible */
    100% {
        height: 40%;
        bottom: 2.5px;
        opacity: 1;
    }

    /* Fill bottom */
}

@keyframes sandBottomFillElegant {
    0% {
        height: 0%;
        bottom: 2.5px;
        opacity: 1;
    }

    45% {
        height: 40%;
        bottom: 2.5px;
        opacity: 1;
    }

    /* Full just before flip */
    45.1%,
    50% {
        height: 0%;
        opacity: 0;
    }

    /* Hide during flip */
    50.1% {
        height: 0%;
        bottom: 50%;
        opacity: 0;
    }

    /* Appear at top */
    55% {
        height: 0%;
        bottom: 50%;
        opacity: 1;
    }

    /* Become visible */
    100% {
        height: 40%;
        bottom: 50%;
        opacity: 1;
    }

    /* Fill top */
}

@keyframes sandStreamFlowElegant {

    0%,
    45% {
        height: 10%;
        opacity: 1;
    }

    /* Flowing */
    45.1%,
    55% {
        height: 0%;
        opacity: 0;
    }

    /* Stop during flip */
    55.1%,
    100% {
        height: 10%;
        opacity: 1;
    }

    /* Flowing again */
}


/* --- Human Biology - Pulsing Heart & ECG --- */
.icon-human-bio .heart-container {
    width: 55px;
    height: 55px;
    /* Slightly larger */
    position: relative;
}

.icon-human-bio .heart-shape {
    width: 44px;
    height: 44px;
    /* Larger heart */
    position: absolute;
    top: 5px;
    left: 5.5px;
    /* Centered */
    /* More organic, powerful beat */
    animation: beatHeartPowerful 1.4s infinite ease-in-out;
    /* Slightly faster */
    filter: drop-shadow(0 3px 5px rgba(236, 72, 153, 0.3));
    /* Pink glow */
}

.icon-human-bio .heart-shape::before,
.icon-human-bio .heart-shape::after {
    content: "";
    position: absolute;
    top: 0;
    width: 22px;
    height: 36px;
    /* Adjusted proportions */
    /* Vibrant red gradient */
    background: linear-gradient(135deg, var(--accent-red), #DC2626);
    border-radius: 22px 22px 0 0;
    transform: rotate(-45deg);
    transform-origin: 0 100%;
}

.icon-human-bio .heart-shape::after {
    left: 0;
    transform: rotate(45deg);
    transform-origin: 100% 100%;
}

/* ECG Line */
.icon-human-bio .ecg-line {
    position: absolute;
    bottom: 5px;
    left: -5%;
    /* Extend slightly */
    width: 110%;
    height: 20px;
    /* Taller ECG area */
    overflow: hidden;
}

.icon-human-bio .ecg-path {
    width: 250%;
    /* Wider for smoother scroll */
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    /* Use SVG for a cleaner, more scalable ECG */
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 50 20'%3E%3Cpath d='M0 15 H 15 L 18 8 L 21 18 L 23 13 L 25 15 H 50' fill='none' stroke='%23059669' stroke-width='1.5'/%3E%3C/svg%3E");
    background-size: 50px 20px;
    /* Size of one ECG cycle */
    background-repeat: repeat-x;
    background-position: 0 bottom;
    /* Sync scroll with heart beat */
    animation: scrollECGDynamic 1.4s infinite linear;
    opacity: 0.9;
    filter: drop-shadow(0 1px 1px rgba(5, 150, 105, 0.3));
    /* Subtle green glow */
}

@keyframes beatHeartPowerful {
    0% {
        transform: scale(1);
        filter: drop-shadow(0 3px 5px rgba(236, 72, 153, 0.3)) brightness(1);
    }

    30% {
        transform: scale(1.15);
        filter: drop-shadow(0 5px 8px rgba(236, 72, 153, 0.5)) brightness(1.1);
    }

    /* Stronger beat, brighter */
    50% {
        transform: scale(0.95);
        filter: drop-shadow(0 2px 4px rgba(236, 72, 153, 0.2)) brightness(0.95);
    }

    /* Contract */
    80% {
        transform: scale(1.02);
    }

    /* Slight rebound */
    100% {
        transform: scale(1);
        filter: drop-shadow(0 3px 5px rgba(236, 72, 153, 0.3)) brightness(1);
    }
}

@keyframes scrollECGDynamic {
    from {
        transform: translateX(0);
    }

    to {
        transform: translateX(-50px);
    }

    /* Scroll one pattern width */
}

/* --- ICT - Dynamic Data Flow Network --- */
.icon-ict .network-grid {
    width: 65px;
    height: 65px;
    /* Larger grid */
    position: relative;
}

.icon-ict .node {
    width: 12px;
    height: 12px;
    /* Larger nodes */
    background: radial-gradient(circle, var(--math-sci-blue-light), var(--math-sci-blue));
    /* Gradient */
    border: 2px solid var(--white);
    border-radius: 50%;
    position: absolute;
    /* More prominent glow */
    box-shadow: 0 0 8px rgba(37, 99, 235, 0.5), 0 0 12px rgba(37, 99, 235, 0.3);
    z-index: 2;
    /* More noticeable pulse */
    animation: pulseNodeICTDynamic var(--medium-duration) infinite ease-in-out;
}

/* Adjusted positions for larger grid */
.icon-ict .node-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.icon-ict .node-tl {
    top: 5px;
    left: 5px;
    animation-delay: 0.3s;
}

.icon-ict .node-tr {
    top: 5px;
    right: 5px;
    animation-delay: 0.6s;
}

.icon-ict .node-bl {
    bottom: 5px;
    left: 5px;
    animation-delay: 0.9s;
}

.icon-ict .node-br {
    bottom: 5px;
    right: 5px;
    animation-delay: 1.2s;
}

.icon-ict .connection {
    position: absolute;
    background-color: var(--grey-medium);
    height: 2px;
    /* Thicker lines */
    transform-origin: left center;
    z-index: 1;
    opacity: 0.8;
    /* Add subtle animation to connections */
    animation: pulseConnectionICT var(--medium-duration) infinite ease-in-out alternate;
}

/* Recalculate line positions/rotations based on new node positions */
/* Using absolute positioning and transforms for lines */
/* Example: Center to Top-Left */
.icon-ict .conn-c-tl {
    width: 35px;
    /* Approximate length */
    top: calc(50% - 1px);
    /* Center vertically */
    left: calc(50% - 17.5px);
    /* Start offset from center */
    transform: rotate(-45deg) translateX(17.5px);
    /* Rotate and adjust */
    transform-origin: center left;
    animation-delay: 0.15s;
}

.icon-ict .conn-c-tr {
    width: 35px;
    top: calc(50% - 1px);
    left: 50%;
    transform: rotate(45deg);
    transform-origin: center left;
    animation-delay: 0.45s;
}

.icon-ict .conn-c-bl {
    width: 35px;
    top: 50%;
    left: calc(50% - 17.5px);
    transform: rotate(45deg) translateX(17.5px);
    transform-origin: center left;
    animation-delay: 0.75s;
}

.icon-ict .conn-c-br {
    width: 35px;
    top: 50%;
    left: 50%;
    transform: rotate(-45deg);
    transform-origin: center left;
    animation-delay: 1.05s;
}

/* Data Packet Animation */
.icon-ict .packet {
    position: absolute;
    width: 6px;
    height: 6px;
    /* Larger packet */
    background-color: var(--accent-pink);
    border-radius: 50%;
    box-shadow: 0 0 6px var(--accent-pink), 0 0 10px var(--accent-pink);
    /* Brighter glow */
    opacity: 0;
    /* Use offset-path for smoother travel along connections */
    /* Fallback: Keyframe animation */
    animation: travelPacketDynamic var(--medium-duration) infinite linear;
    z-index: 3;
    filter: blur(0.5px);
    /* Set initial position */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Keyframes for each path (fallback) */
@keyframes travelPacketDynamic {
    0% {
        top: 50%;
        left: 50%;
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }

    5% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    /* Appear at center */
    /* Move towards TL */
    25% {
        top: calc(5px + 6px);
        left: calc(5px + 6px);
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    30% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }

    /* Disappear at TL */
    /* Appear at center again */
    31% {
        top: 50%;
        left: 50%;
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }

    36% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    /* Move towards TR */
    56% {
        top: calc(5px + 6px);
        left: calc(100% - 5px - 6px);
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    61% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }

    /* Similar pattern for BL and BR */
    100% {
        opacity: 0;
    }
}

/* Assign delays to different packets if needed */
.icon-ict .packet1 {
    animation-delay: 0.1s;
}

.icon-ict .packet2 {
    animation-delay: 0.7s;
    animation-duration: 2.8s;
}

/* Example variation */


@keyframes pulseNodeICTDynamic {

    0%,
    100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 8px rgba(37, 99, 235, 0.5), 0 0 12px rgba(37, 99, 235, 0.3);
    }

    50% {
        transform: translate(-50%, -50%) scale(1.15);
        box-shadow: 0 0 12px rgba(37, 99, 235, 0.7), 0 0 18px rgba(37, 99, 235, 0.5);
    }

    /* Stronger pulse */
}

@keyframes pulseConnectionICT {

    0%,
    100% {
        opacity: 0.6;
    }

    50% {
        opacity: 0.9;
    }

    /* Subtle pulse */
}


/* --- Language - Expressive Speech Bubbles --- */
.icon-language .bubble-container {
    width: 65px;
    height: 55px;
    /* Larger */
    position: relative;
}

.icon-language .bubble-shape {
    position: absolute;
    background-color: var(--humanities-purple);
    border-radius: 14px;
    /* More rounded */
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 12px;
    /* Larger text */
    font-weight: 600;
    /* Bolder */
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    /* Enhanced shadow */
    /* More expressive morphing */
    animation: morphBubbleExpressive var(--slow-duration) infinite ease-in-out;
    transform-origin: center center;
}

.icon-language .bubble1 {
    width: 45px;
    height: 32px;
    /* Adjusted size */
    top: 3px;
    left: 0px;
    animation-delay: 0s;
}

.icon-language .bubble1::before {
    /* Speech tail */
    content: '';
    position: absolute;
    bottom: -8px;
    left: 14px;
    /* Adjusted position */
    width: 0;
    height: 0;
    border-left: 9px solid transparent;
    border-right: 9px solid transparent;
    border-top: 10px solid var(--humanities-purple);
    transform-origin: top center;
    /* More lively tail wiggle */
    animation: wiggleTailLively var(--slow-duration) infinite ease-in-out;
    animation-delay: 0s;
}

.icon-language .bubble2 {
    width: 36px;
    height: 28px;
    /* Adjusted size */
    bottom: 0px;
    right: 2px;
    background-color: var(--humanities-purple-light);
    color: var(--text-dark);
    animation-delay: -2.5s;
    /* Offset animation */
}

.icon-language .bubble2::before {
    /* Speech tail */
    content: '';
    position: absolute;
    bottom: -7px;
    right: 12px;
    /* Adjusted position */
    width: 0;
    height: 0;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 8px solid var(--humanities-purple-light);
    transform-origin: top center;
    animation: wiggleTailLively var(--slow-duration) infinite ease-in-out;
    animation-delay: -2.5s;
}

/* Add text inside bubbles */
.icon-language .bubble1::after {
    content: '你好';
    animation: fadeTextBubble 6s infinite ease-in-out 0s;
}

/* Example text */
.icon-language .bubble2::after {
    content: 'Salut!';
    animation: fadeTextBubble 6s infinite ease-in-out -2.5s;
}

/* Example text */

@keyframes morphBubbleExpressive {

    0%,
    100% {
        border-radius: 14px 14px 14px 8px;
        transform: translateY(0) rotate(-2deg) scale(1);
    }

    /* Default speech bubble */
    25% {
        border-radius: 50%;
        transform: translateY(-4px) rotate(3deg) scale(1.05);
    }

    /* Round, pop up */
    50% {
        border-radius: 8px 14px 8px 14px;
        transform: translateY(3px) rotate(-4deg) scale(0.97);
    }

    /* Squarish, dip down */
    75% {
        border-radius: 10px;
        transform: translateY(-2px) rotate(1deg) scale(1.02);
    }

    /* Slightly squarish, lift */
}

@keyframes wiggleTailLively {

    0%,
    100% {
        transform: rotate(-8deg) scaleX(1);
    }

    25% {
        transform: rotate(8deg) scaleX(1.1);
    }

    /* Wiggle further, slight stretch */
    50% {
        transform: rotate(0deg) scaleX(0.9);
    }

    /* Center, slight squash */
    75% {
        transform: rotate(-6deg) scaleX(1.05);
    }
}

@keyframes fadeTextBubble {

    /* Fade text in/out slightly */
    0%,
    10%,
    90%,
    100% {
        opacity: 0.7;
    }

    50% {
        opacity: 1;
    }
}

/* --- Literature - Animated Book --- */
.icon-literature .book-cover {
    width: 50px;
    height: 60px;
    /* Taller book */
    background: linear-gradient(45deg, #8B4513, #A0522D);
    /* SaddleBrown/Sienna */
    border-radius: 4px 8px 8px 4px;
    /* Adjusted rounding for spine */
    position: relative;
    perspective: 500px;
    /* More perspective */
    box-shadow: 3px 3px 8px rgba(0, 0, 0, 0.2);
    /* Stronger shadow */
    transform: rotateY(-10deg);
    /* Slight angle */
}

.icon-literature .page {
    position: absolute;
    width: 94%;
    height: 92%;
    /* Adjusted size */
    top: 4%;
    left: 3%;
    /* Adjust position */
    background-color: #FFFBF0;
    /* Warmer paper */
    border: 1px solid #F5F5F5;
    transform-origin: left center;
    border-radius: 0 4px 4px 0;
    /* Softer rounding */
    /* More realistic page turn */
    animation: turnPageRealistic var(--slow-duration) infinite ease-in-out;
    box-shadow: inset 1.5px 0px 3px rgba(0, 0, 0, 0.05);
    /* More visible spine shadow */
}

/* Page stack effect with slight offsets */
.icon-literature .page1 {
    z-index: 4;
    animation-delay: 0s;
}

.icon-literature .page2 {
    z-index: 3;
    animation-delay: -0.15s;
    background-color: #FFFDF5;
    transform: translateX(0.5px) translateY(0.5px);
}

.icon-literature .page3 {
    z-index: 2;
    animation-delay: -0.3s;
    transform: translateX(1px) translateY(1px);
}

.icon-literature .page4 {
    z-index: 1;
    animation-delay: -0.45s;
    background-color: #FFFDF5;
    transform: translateX(1.5px) translateY(1.5px);
    animation: none;
}

/* Static back page */

/* Text lines with subtle animation */
.icon-literature .page::before,
.icon-literature .page::after {
    content: '';
    position: absolute;
    left: 12%;
    right: 12%;
    height: 1.5px;
    /* Thicker lines */
    background-color: rgba(0, 0, 0, 0.3);
    /* Darker text */
    animation: fadeTextRealistic var(--slow-duration) infinite ease-in-out;
    animation-delay: inherit;
    /* Inherit page delay */
    transform-origin: left center;
}

.icon-literature .page::before {
    top: 25%;
    width: 70%;
}

.icon-literature .page::after {
    top: 45%;
    width: 80%;
}

@keyframes turnPageRealistic {

    0%,
    10% {
        transform: rotateY(0deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px));
    }

    /* Closed */
    50% {
        transform: rotateY(-175deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px));
    }

    /* Fully open, slight curve */
    90%,
    100% {
        transform: rotateY(0deg) translateX(var(--tx, 0px)) translateY(var(--ty, 0px));
    }

    /* Closed */
}

/* Assign offsets via CSS variables */
.icon-literature .page2 {
    --tx: 0.5px;
    --ty: 0.5px;
}

.icon-literature .page3 {
    --tx: 1px;
    --ty: 1px;
}

@keyframes fadeTextRealistic {

    0%,
    15%,
    85%,
    100% {
        opacity: 0;
        transform: scaleX(0.8);
    }

    /* Fade and shrink */
    50% {
        opacity: 0.6;
        transform: scaleX(1);
    }

    /* Visible when open */
}


/* --- Mathematics - Glowing Pi Symbol --- */
.icon-math .pi-container {
    width: 55px;
    height: 55px;
    /* Larger */
    position: relative;
    perspective: 600px;
}

.icon-math .pi-symbol {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotateY(0deg);
    /* Initial state */
    font-size: 52px;
    /* Larger */
    font-weight: 500;
    font-family: 'Georgia', 'Times New Roman', Times, serif;
    color: var(--humanities-purple);
    /* Stronger, pulsing glow */
    text-shadow: 0 0 10px var(--humanities-purple-light), 0 0 20px rgba(124, 58, 237, 0.6);
    /* Pulse and subtle 3D rotation */
    animation: pulsePiDynamic var(--medium-duration) infinite ease-in-out;
    z-index: 2;
}

/* Orbiting numbers */
.icon-math .orbiting-num {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 18px;
    height: 18px;
    /* Larger */
    margin-left: -9px;
    margin-top: -9px;
    color: var(--humanities-purple);
    font-size: 10px;
    /* Larger font */
    font-weight: bold;
    text-align: center;
    line-height: 18px;
    /* Smoother orbit with perspective */
    animation: orbitPiDynamic var(--slow-duration) infinite linear;
    transform-style: preserve-3d;
    z-index: 1;
    opacity: 0.9;
    background-color: rgba(255, 255, 255, 0.6);
    /* Slight background */
    border-radius: 50%;
    box-shadow: 0 0 3px rgba(124, 58, 237, 0.3);
}

/* Define content in HTML */
.icon-math .num1 {
    animation-delay: 0s;
}

.icon-math .num2 {
    animation-delay: -0.8s;
}

.icon-math .num3 {
    animation-delay: -1.6s;
}

.icon-math .num4 {
    animation-delay: -2.4s;
}

.icon-math .num5 {
    animation-delay: -3.2s;
}

@keyframes pulsePiDynamic {

    0%,
    100% {
        transform: translate(-50%, -50%) scale(1) rotateY(0deg);
        text-shadow: 0 0 10px var(--humanities-purple-light), 0 0 20px rgba(124, 58, 237, 0.6);
    }

    50% {
        transform: translate(-50%, -50%) scale(1.1) rotateY(15deg);
        text-shadow: 0 0 15px var(--humanities-purple), 0 0 30px var(--humanities-purple);
    }

    /* Pulse, glow brighter, rotate slightly */
}

@keyframes orbitPiDynamic {
    from {
        transform: rotateY(0deg) translateX(30px) rotateY(0deg) rotateX(15deg) translateZ(5px);
        opacity: 0.7;
    }

    /* Wider orbit, more tilt, slight Z */
    to {
        transform: rotateY(360deg) translateX(30px) rotateY(-360deg) rotateX(15deg) translateZ(5px);
        opacity: 0.9;
    }
}

/* --- Physics - Dynamic Atom --- */
.icon-physics .atom-core {
    width: 60px;
    height: 60px;
    /* Larger core */
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    /* More dramatic tilt */
    transform: rotateX(60deg) rotateZ(-15deg) scale(1.1);
    perspective: 700px;
    /* More perspective */
    transform-style: preserve-3d;
    /* Apply to parent */
}

.icon-physics .nucleus {
    width: 12px;
    height: 12px;
    /* Larger nucleus */
    background: radial-gradient(circle, #FDE68A, var(--business-econ-orange));
    /* Brighter gradient */
    border-radius: 50%;
    position: absolute;
    /* Keep absolute */
    /* Position in 3D center */
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) translateZ(0);
    box-shadow: 0 0 8px var(--business-econ-orange), 0 0 12px #FBBF24;
    /* Stronger glow */
    z-index: 5;
    /* Add subtle pulse to nucleus */
    animation: pulseNucleusPhysics var(--fast-duration) infinite ease-in-out;
}

.icon-physics .orbit {
    position: absolute;
    border: 1.5px solid var(--grey-medium);
    /* Thicker orbit */
    border-radius: 50%;
    width: 55px;
    height: 55px;
    /* Larger orbits */
    top: 50%;
    left: 50%;
    /* Center orbits and apply rotations */
    transform-origin: center center;
    transform-style: preserve-3d;
    /* Inherit preserve-3d */
    opacity: 0.7;
}

/* Define orbit rotations directly */
.icon-physics .orbit1 {
    transform: translate(-50%, -50%) rotateZ(0deg);
}

.icon-physics .orbit2 {
    transform: translate(-50%, -50%) rotateY(65deg) rotateX(5deg);
}

/* More tilt */
.icon-physics .orbit3 {
    transform: translate(-50%, -50%) rotateY(-65deg) rotateX(-5deg);
}

.icon-physics .electron {
    width: 6px;
    height: 6px;
    /* Larger electrons */
    background-color: var(--math-sci-blue);
    border-radius: 50%;
    position: absolute;
    /* Start at the edge of the orbit path */
    top: 50%;
    left: calc(50% + 27.5px);
    /* Half orbit width */
    margin: -3px 0 0 -3px;
    /* Center electron */
    box-shadow: 0 0 5px var(--math-sci-blue), 0 0 8px var(--math-sci-blue-light);
    /* Enhanced glow */
    transform-style: preserve-3d;
    transform-origin: -27.5px center;
    /* Set origin to center of orbit */
}

/* Assign electrons to orbits with different speeds/delays */
.icon-physics .electron1 {
    animation: electronOrbitPhysics1 var(--fast-duration) infinite linear;
}

.icon-physics .electron2 {
    animation: electronOrbitPhysics2 var(--medium-duration) infinite linear;
    animation-delay: -1s;
}

.icon-physics .electron3 {
    animation: electronOrbitPhysics3 var(--slow-duration) infinite linear;
    animation-delay: -2s;
}

/* Keyframes define rotation around the orbit's axis */
/* Apply orbit rotations within the animation */
@keyframes electronOrbitPhysics1 {

    /* Orbit 1 (flat Z rotation) */
    from {
        transform: rotateZ(0deg);
    }

    to {
        transform: rotateZ(360deg);
    }
}

@keyframes electronOrbitPhysics2 {

    /* Orbit 2 (rotated Y 65deg X 5deg) */
    from {
        transform: rotateY(65deg) rotateX(5deg) rotateZ(0deg);
    }

    to {
        transform: rotateY(65deg) rotateX(5deg) rotateZ(360deg);
    }
}

@keyframes electronOrbitPhysics3 {

    /* Orbit 3 (rotated Y -65deg X -5deg) */
    from {
        transform: rotateY(-65deg) rotateX(-5deg) rotateZ(0deg);
    }

    to {
        transform: rotateY(-65deg) rotateX(-5deg) rotateZ(360deg);
    }
}

@keyframes pulseNucleusPhysics {

    0%,
    100% {
        transform: translate(-50%, -50%) translateZ(0) scale(1);
    }

    50% {
        transform: translate(-50%, -50%) translateZ(0) scale(1.1);
    }
}


/* --- Psychology - Dynamic Brain --- */
.icon-psychology .brain-container {
    width: 55px;
    height: 50px;
    position: relative;
    perspective: 600px;
}

.icon-psychology .brain-half {
    width: 28px;
    height: 45px;
    position: absolute;
    top: 2px;
    border: 2px solid var(--humanities-purple);
    border-radius: 50% 50% 45% 45% / 65% 65% 40% 40%;
    background: linear-gradient(135deg, var(--humanities-purple-light), #E9D5FF);
    animation: pulseBrainPsyDynamic var(--medium-duration) infinite alternate ease-in-out;
    overflow: hidden;
    box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05);
    transform-style: preserve-3d;
}

.icon-psychology .left {
    left: 2px;
    transform: rotate(-10deg);
    animation-delay: 0.3s;
    z-index: 1;
}

.icon-psychology .right {
    right: 2px;
    transform: rotate(10deg);
    z-index: 2;
}

.icon-psychology .brain-half::before {
    content: '';
    position: absolute;
    width: 160%;
    height: 160%;
    /* Larger to ensure coverage */
    top: -30%;
    left: -30%;
    /* More complex pattern for folds */
    background:
        repeating-linear-gradient(-45deg,
            rgba(124, 58, 237, 0.15),
            /* Darker lines */
            rgba(124, 58, 237, 0.15) 2px,
            transparent 2px,
            transparent 7px
            /* Wider gaps */
        ),
        repeating-linear-gradient(45deg,
            rgba(124, 58, 237, 0.05),
            /* Fainter cross lines */
            rgba(124, 58, 237, 0.05) 1px,
            transparent 1px,
            transparent 5px);
    /* Slower, swirling movement */
    animation: moveFoldsSwirl var(--very-slow-duration) infinite linear;
    opacity: 0.8;
}

@keyframes moveFoldsSwirl {
    from {
        transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
    }

    to {
        transform: rotate(360deg) scale(1.05) translateX(5px) translateY(-5px);
    }

    /* Rotate, scale, and translate */
}

/* Neural connections between brain halves */
.icon-psychology .synapse {
    position: absolute;
    width: 4px;
    height: 4px;
    /* Larger synapses */
    background-color: var(--accent-pink);
    border-radius: 50%;
    /* Brighter, more impactful glow */
    box-shadow: 0 0 6px var(--accent-pink), 0 0 10px var(--accent-pink), 0 0 15px #F472B6;
    opacity: 0;
    /* Faster, brighter flash */
    animation: fireSynapseImpact var(--fast-duration) infinite ease-out;
    filter: blur(0.5px);
    z-index: 3;
    /* Ensure synapses are on top */
}

/* Neural connections between brain halves */
.icon-psychology .synapse::before {
    content: '';
    position: absolute;
    width: 15px;
    height: 1px;
    background-color: var(--accent-pink);
    top: 50%;
    left: 50%;
    transform-origin: left center;
    opacity: 0;
    animation: pulseConnection var(--fast-duration) infinite ease-out;
    animation-delay: inherit;
    box-shadow: 0 0 3px var(--accent-pink);
}

/* More synapse points */
.icon-psychology .synapse1 {
    top: 20%;
    left: 50%;
    animation-delay: 0.2s;
}

.icon-psychology .synapse1::before {
    transform: translateX(-50%) rotate(30deg);
}

.icon-psychology .synapse2 {
    top: 60%;
    left: 50%;
    animation-delay: 0.8s;
}

.icon-psychology .synapse2::before {
    transform: translateX(-50%) rotate(-20deg);
}

.icon-psychology .synapse3 {
    top: 40%;
    left: 50%;
    animation-delay: 1.4s;
}

.icon-psychology .synapse3::before {
    transform: translateX(-50%) rotate(10deg);
}

.icon-psychology .synapse4 {
    top: 80%;
    left: 50%;
    animation-delay: 2.0s;
}

.icon-psychology .synapse4::before {
    transform: translateX(-50%) rotate(-40deg);
}

@keyframes pulseBrainPsyDynamic {
    from {
        transform: scale(1);
        box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08), 0 0 10px rgba(124, 58, 237, 0.3);
    }

    to {
        transform: rotate(-7deg) scale(1.05);
        box-shadow: inset 0 0 12px rgba(0, 0, 0, 0.08), 0 0 15px rgba(124, 58, 237, 0.5);
    }

    /* Stronger pulse and glow */
}

@keyframes pulseConnection {
    0% {
        opacity: 0;
        transform-origin: left center;
        transform: scaleX(0);
    }

    50% {
        opacity: 1;
        transform: scaleX(1);
    }

    100% {
        opacity: 0;
        transform: scaleX(0.8);
    }
}

@keyframes fireSynapseImpact {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }

    10% {
        opacity: 1;
        transform: scale(1.2);
    }

    /* Quick, bright flash, slightly larger */
    25% {
        opacity: 0.5;
        transform: scale(0.8);
    }

    /* Fade quickly */
    40% {
        opacity: 0;
        transform: scale(0.5);
    }

    100% {
        opacity: 0;
    }
}

/* --- Sociology - Interactive Figures --- */
.icon-sociology .figure-group {
    width: 65px;
    height: 50px;
    /* Wider */
    position: relative;
    display: flex;
    justify-content: space-around;
    align-items: flex-end;
}

.icon-sociology .figure {
    width: 13px;
    height: 32px;
    /* Slimmer, taller figures */
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    /* More interactive animation */
    animation: interactFigureDynamic var(--slow-duration) infinite ease-in-out;
    transform-origin: bottom center;
    /* Animate from base */
}

.icon-sociology .figure-head {
    width: 11px;
    height: 11px;
    /* Slightly larger head */
    border-radius: 50%;
    background-color: inherit;
    margin-bottom: -2px;
    z-index: 1;
    box-shadow: inset 0 -1px 2px rgba(0, 0, 0, 0.1);
    /* Subtle shadow on head */
}

.icon-sociology .figure-body {
    width: 100%;
    height: 22px;
    /* Taller body */
    border-radius: 5px 5px 2px 2px;
    /* Adjusted rounding */
    background-color: inherit;
    box-shadow: inset 0 2px 3px rgba(255, 255, 255, 0.1);
    /* Subtle highlight */
}

/* Assign colors and animation delays/offsets */
.icon-sociology .figure1 {
    animation-delay: 0s;
}

.icon-sociology .figure1 .figure-head,
.icon-sociology .figure1 .figure-body {
    background: linear-gradient(var(--math-sci-blue), #3B82F6);
}

/* Gradient */
.icon-sociology .figure2 {
    animation-delay: -1s;
    height: 38px;
}

/* Taller */
.icon-sociology .figure2 .figure-head,
.icon-sociology .figure2 .figure-body {
    background: linear-gradient(var(--bio-chem-green), #10B981);
}

.icon-sociology .figure3 {
    animation-delay: -2s;
}

.icon-sociology .figure3 .figure-head,
.icon-sociology .figure3 .figure-body {
    background: linear-gradient(var(--business-econ-orange), #FBBF24);
}

.icon-sociology .figure4 {
    animation-delay: -3s;
    height: 30px;
}

/* Shorter */
.icon-sociology .figure4 .figure-head,
.icon-sociology .figure4 .figure-body {
    background: linear-gradient(var(--humanities-purple), #A78BFA);
}

@keyframes interactFigureDynamic {

    0%,
    100% {
        transform: translateY(0) scaleY(1) rotate(0deg);
    }

    25% {
        transform: translateY(-5px) scaleY(1.03) rotate(-3deg);
    }

    /* Rise, stretch, slight lean */
    50% {
        transform: translateY(0) scaleY(1) rotate(0deg);
    }

    75% {
        transform: translateY(-3px) scaleY(0.97) rotate(3deg);
    }

    /* Dip, squash, lean other way */
}

/* --- Statistics - Dynamic Bell Curve --- */
.icon-stats .stats-container {
    width: 65px;
    height: 50px;
    /* Larger */
    position: relative;
}

.icon-stats .axis-x {
    position: absolute;
    bottom: 10px;
    left: 2px;
    /* Adjusted position */
    width: calc(100% - 4px);
    height: 2px;
    /* Thicker axis */
    background-color: var(--grey-medium);
    border-radius: 1px;
}

.icon-stats .axis-y {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 2px;
    height: 35px;
    /* Thicker, taller */
    background-color: var(--grey-medium);
    border-radius: 1px;
}

.icon-stats .bell-curve {
    position: absolute;
    bottom: 11px;
    /* Sit on X axis */
    left: 0;
    width: 100%;
    height: 34px;
    /* Adjusted height */
    overflow: hidden;
}

.icon-stats .bell-curve svg {
    /* Use SVG for smooth curve */
    width: 100%;
    height: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    overflow: visible;
}

.icon-stats .bell-curve path {
    stroke: var(--humanities-purple);
    stroke-width: 2.5;
    /* Thicker curve */
    fill: rgba(124, 58, 237, 0.1);
    /* Subtle fill */
    stroke-linecap: round;
    stroke-dasharray: 200;
    /* Estimate based on path */
    stroke-dashoffset: 200;
    /* Smooth drawing animation */
    animation: drawBellCurveDynamic var(--medium-duration) infinite ease-in-out;
    filter: drop-shadow(0 1px 3px rgba(124, 58, 237, 0.3));
}

@keyframes drawBellCurveDynamic {
    0% {
        stroke-dashoffset: 200;
        fill-opacity: 0;
    }

    50% {
        stroke-dashoffset: 0;
        fill-opacity: 0.1;
    }

    /* Draw and fill */
    70% {
        stroke-dashoffset: 0;
        fill-opacity: 0.1;
    }

    /* Hold */
    100% {
        stroke-dashoffset: -200;
        fill-opacity: 0;
    }

    /* Erase */
}

/* Animated data points */
.icon-stats .data-point {
    position: absolute;
    width: 5px;
    height: 5px;
    /* Larger points */
    background-color: var(--accent-pink);
    border-radius: 50%;
    bottom: 11px;
    /* Start on axis */
    opacity: 0;
    /* Dynamic plotting animation */
    animation: plotDataPointDynamic var(--medium-duration) infinite ease-out;
    filter: blur(0.5px);
    box-shadow: 0 0 4px var(--accent-pink);
    /* Add glow */
    /* Use CSS variable for X position */
    left: calc(var(--x-pos, 0.5) * (100% - 5px));
    /* Position relative to container width */
}

/* Set delays and --x-pos inline in HTML */

@keyframes plotDataPointDynamic {
    0% {
        bottom: 11px;
        opacity: 0;
        transform: scale(0.3) rotate(0deg);
    }

    15% {
        opacity: 1;
        transform: scale(1.1) rotate(0deg);
    }

    /* Appear with slight pop */
    50% {
        /* Rise to approximate curve height - adjust multiplier as needed */
        bottom: calc(11px + 30px * (1 - pow(abs(var(--x-pos, 0.5) - 0.5) * 2, 2)));
        opacity: 1;
        transform: scale(1) rotate(360deg);
        /* Rise and spin */
    }

    85% {
        opacity: 0;
        transform: scale(0.5) rotate(720deg);
    }

    /* Fade out */
    100% {
        bottom: 11px;
        opacity: 0;
    }
}