.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Animated Icons */
.icon-container {
  width: 80px;
  height: 80px;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(160, 235, 235, 0.2), rgba(160, 235, 235, 0.05));
  box-shadow: 0 4px 15px rgba(160, 235, 235, 0.2);
  overflow: hidden;
}

/* Icon container shine effect */
.icon-container::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  animation: iconShine 4s infinite ease-in-out;
}

@keyframes iconShine {
  0%, 100% { opacity: 0; transform: scale(0.8); }
  50% { opacity: 0.8; transform: scale(1); }
}

/* Shield Icon Animation */
.shield-icon {
  position: relative;
  animation: shieldPulse 3s infinite;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
}

@keyframes shieldPulse {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4))
           drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  }
  50% {
    transform: scale(1.15);
    filter: drop-shadow(0 0 15px rgba(160, 235, 235, 0.8))
           drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
           drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  }
}

/* Shield particles */
.shield-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle, rgba(160, 235, 235, 0.8) 0%, transparent 70%);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(0);
  opacity: 0;
  animation: shieldRipple 3s infinite;
}

@keyframes shieldRipple {
  0%, 100% { transform: translate(-50%, -50%) scale(0); opacity: 0; }
  25% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.4; }
  50% { transform: translate(-50%, -50%) scale(1.8); opacity: 0; }
}

/* User Circle Animation */
.user-circle-icon {
  animation: userSpin 6s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
}

@keyframes userSpin {
  0% {
    transform: rotateY(0deg) scale(1);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4));
  }
  50% {
    transform: rotateY(180deg) scale(1.1);
    filter: drop-shadow(0 0 15px rgba(160, 235, 235, 0.8));
  }
  100% {
    transform: rotateY(360deg) scale(1);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4));
  }
}

/* User Circle glow effect */
.user-circle-icon::before {
  content: '';
  position: absolute;
  inset: -5px;
  border-radius: 50%;
  background: conic-gradient(from 0deg, transparent, rgba(160, 235, 235, 0.8), transparent);
  opacity: 0;
  animation: userGlow 4s infinite;
}

@keyframes userGlow {
  0%, 100% { opacity: 0; transform: rotate(0deg); }
  25%, 75% { opacity: 0.5; }
  50% { opacity: 0.8; transform: rotate(360deg); }
}

/* Clock Animation */
.clock-icon {
  animation: clockTick 8s infinite linear;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  position: relative;
}

@keyframes clockTick {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Clock ticking effect */
.clock-icon::before, .clock-icon::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  z-index: -1;
}

.clock-icon::before {
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(160, 235, 235, 0.2) 0%, transparent 70%);
  animation: clockPulse 2s infinite;
}

.clock-icon::after {
  width: 150%;
  height: 150%;
  border: 2px solid rgba(160, 235, 235, 0.3);
  animation: clockRing 4s infinite;
}

@keyframes clockPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0.3; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0.6; }
}

@keyframes clockRing {
  0% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
  25% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
  50% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
  100% { transform: translate(-50%, -50%) scale(0.8); opacity: 0; }
}

/* Graduation Cap Animation */
.graduation-cap-icon {
  animation: capFloat 4s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  position: relative;
}

@keyframes capFloat {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4));
  }
  25% {
    transform: translateY(-5px) rotate(-5deg);
  }
  50% {
    transform: translateY(-12px) rotate(0deg);
    filter: drop-shadow(0 0 15px rgba(160, 235, 235, 0.8));
  }
  75% {
    transform: translateY(-5px) rotate(5deg);
  }
}

/* Graduation Cap particles */
.graduation-cap-icon::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  width: 120%;
  height: 10px;
  background: linear-gradient(to bottom, rgba(160, 235, 235, 0.8), transparent);
  transform: translateX(-50%);
  clip-path: polygon(0% 0%, 100% 0%, 80% 100%, 20% 100%);
  opacity: 0;
  animation: capTrail 4s infinite;
}

@keyframes capTrail {
  0%, 100% { opacity: 0; }
  40%, 60% { opacity: 0.6; }
}

/* File Text Animation */
.file-text-icon {
  animation: fileWave 3s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  position: relative;
}

@keyframes fileWave {
  0%, 100% {
    transform: skewX(0deg) scale(1);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4));
  }
  25% {
    transform: skewX(5deg) scale(1.05);
    filter: drop-shadow(0 0 10px rgba(160, 235, 235, 0.6));
  }
  50% {
    transform: skewX(0deg) scale(1.1);
    filter: drop-shadow(0 0 15px rgba(160, 235, 235, 0.8));
  }
  75% {
    transform: skewX(-5deg) scale(1.05);
    filter: drop-shadow(0 0 10px rgba(160, 235, 235, 0.6));
  }
}

/* File Text lines effect */
.file-text-icon::before, .file-text-icon::after {
  content: '';
  position: absolute;
  left: 50%;
  height: 2px;
  background: rgba(160, 235, 235, 0.8);
  transform: translateX(-50%);
  opacity: 0;
}

.file-text-icon::before {
  top: 30%;
  width: 60%;
  animation: fileLine1 3s infinite;
}

.file-text-icon::after {
  top: 60%;
  width: 40%;
  animation: fileLine2 3s infinite;
}

@keyframes fileLine1 {
  0%, 100% { opacity: 0; width: 40%; }
  30%, 70% { opacity: 0.8; width: 60%; }
}

@keyframes fileLine2 {
  0%, 100% { opacity: 0; width: 30%; }
  40%, 80% { opacity: 0.6; width: 50%; }
}

/* Users Animation */
.users-icon {
  animation: usersScale 4s infinite ease-in-out;
  filter: drop-shadow(0 0 8px rgba(160, 235, 235, 0.6))
         drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
  position: relative;
}

@keyframes usersScale {
  0%, 100% {
    transform: scale(1) translateY(0);
    filter: drop-shadow(0 0 5px rgba(160, 235, 235, 0.4));
  }
  25% {
    transform: scale(1.05) translateY(-3px);
  }
  50% {
    transform: scale(1.15) translateY(-6px);
    filter: drop-shadow(0 0 15px rgba(160, 235, 235, 0.8));
  }
  75% {
    transform: scale(1.05) translateY(-3px);
  }
}

/* Users connection effect */
.users-icon::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 140%;
  height: 140%;
  border-radius: 50%;
  border: 2px dashed rgba(160, 235, 235, 0.4);
  transform: translate(-50%, -50%);
  opacity: 0;
  animation: usersConnect 4s infinite;
}

@keyframes usersConnect {
  0%, 100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8) rotate(0deg); }
  25%, 75% { opacity: 0.6; }
  50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.2) rotate(180deg); }
}

/* Card flip animation enhancement */
.preserve-3d {
  transition: transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

/* Enhanced visibility for celeste icons with subtle shadow */
.icon-container svg {
  filter: drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
}

/* Subtle shadow utility class for better visibility */
.subtle-shadow {
  filter: drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2));
}

/* Combine subtle shadow with glow effect */
.subtle-shadow-glow {
  filter: drop-shadow(1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(1px -1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(-1px 1px 0 rgba(0, 0, 0, 0.2))
         drop-shadow(0 0 8px rgba(160, 235, 235, 0.6));
}