@keyframes menuFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.nav-item:hover {
  animation: menuFloat 1s ease infinite;
}

.dropdown-enter {
  opacity: 0;
  transform: translateY(-10px);
}

.dropdown-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 200ms, transform 200ms;
}

.dropdown-exit {
  opacity: 1;
}

.dropdown-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 200ms, transform 200ms;
}

/* Gradient animation for active links */
.nav-link-active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #9333ea, #ec4899);
  animation: gradientMove 3s linear infinite;
}

@keyframes gradientMove {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
} 