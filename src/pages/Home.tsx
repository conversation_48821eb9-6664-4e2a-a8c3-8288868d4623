import Hero from '../components/home/<USER>';
import Subjects from '../components/home/<USER>';
import WhyUs from '../components/home/<USER>';
import DoubtSection from '../components/home/<USER>';
import GroupDiscount from '../components/home/<USER>';
import Testimonials from '../components/home/<USER>';
import FAQSection from '../components/home/<USER>';
import CoreServices from '../components/home/<USER>';
import HowItWorks from '../components/home/<USER>';
import AssuredQuality from '../components/home/<USER>';
import ComputerScienceHelp from '../components/home/<USER>';

// Removed unused trackVisitor import

export default function Home() {
  // Removed trackVisitor usage

  return (
    <>
      {/* Using a fragment <></> is fine, or you can use a div if needed */}
      <div className="relative overflow-x-hidden"> {/* Added overflow-x-hidden for safety */}
        <Hero />
        <CoreServices />
        <HowItWorks />
        <ComputerScienceHelp />
        <AssuredQuality />
        <Subjects />
        <WhyUs />
        <DoubtSection />
        <GroupDiscount />
        <Testimonials />
        <FAQSection />
      </div>
    </>
  );
}